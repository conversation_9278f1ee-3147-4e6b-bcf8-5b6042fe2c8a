# 🐍 Psychedelic Snake Music Visualizer

A trippy, psychedelic music visualizer that connects to your Spotify account and creates mesmerizing snake animations that react to your music in real-time!

## ✨ Features

- 🎵 **Spotify Integration**: Connects to your Spotify account to get real-time music data
- 🐍 **Psychedelic Snakes**: Multiple animated snakes that move and change based on your music
- 🎨 **Audio-Reactive Visuals**:
  - Snake movement speed syncs to tempo
  - Colors change based on valence (mood)
  - Snake thickness responds to energy levels
  - Length varies with loudness
- 🌈 **Trippy Effects**: Glowing trails, color cycling, morphing shapes, and particle effects
- 📱 **Responsive Design**: Works on desktop and mobile devices

## 🚀 Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd music_visualizer
npm install
```

### 2. Create Spotify App

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Click "Create an App"
3. Fill in the app details:
   - **App Name**: Psychedelic Snake Visualizer
   - **App Description**: Music visualizer with trippy snake animations
4. Once created, note your **Client ID**
5. Click "Edit Settings" and add redirect URI: `http://localhost:5173`

### 3. Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your Spotify credentials:
   ```
   VITE_SPOTIFY_CLIENT_ID=your_spotify_client_id_here
   VITE_SPOTIFY_REDIRECT_URI=http://localhost:5173/callback
   ```

### 4. Run the Application

```bash
npm run dev
```

Open [http://localhost:5173](http://localhost:5173) in your browser.

## 🎮 How to Use

1. **Connect Spotify**: Click "Connect with Spotify" and authorize the app
2. **Start Playing Music**: Open Spotify and start playing any song
3. **Watch the Magic**: The snakes will start moving and changing based on your music!

## 🎵 Audio Analysis Features

The visualizer uses Spotify's audio analysis to create reactive animations:

- **Energy**: Controls snake movement intensity and glow effects
- **Valence**: Determines color schemes (happy = bright colors, sad = cooler tones)
- **Tempo**: Affects snake movement speed and animation timing
- **Loudness**: Influences snake length and particle density

## 🛠 Technical Details

- **Frontend**: React + Vite
- **Canvas Rendering**: HTML5 Canvas with custom animation engine
- **API**: Spotify Web API for music data and audio analysis
- **Styling**: CSS with glassmorphism effects and responsive design

## 🎨 Customization

You can customize the visualizer by modifying:

- `src/utils/snakeEngine.js`: Snake behavior and visual effects
- `src/App.css`: Styling and color schemes
- Number of snakes, particle effects, and animation parameters

## 📱 Browser Compatibility

- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Supported (some visual effects may vary)
- Mobile browsers: Supported with touch-friendly interface

## 🔧 Troubleshooting

**"Authentication failed"**: Check your Spotify Client ID and redirect URI
**"No music playing"**: Make sure Spotify is actively playing music
**"Failed to get current track"**: Ensure you have an active Spotify Premium account

## 📄 License

MIT License - feel free to use and modify!
