import { useEffect, useRef, useState } from 'react';
import { SnakeEngine } from '../utils/snakeEngine';
import spotifyApi from '../utils/spotifyApi';

const SnakeVisualizer = () => {
  const canvasRef = useRef(null);
  const engineRef = useRef(null);
  const animationRef = useRef(null);
  const [currentTrack, setCurrentTrack] = useState(null);
  const [audioFeatures, setAudioFeatures] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Initialize canvas
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      if (engineRef.current) {
        engineRef.current.resize(canvas.width, canvas.height);
      }
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize snake engine
    engineRef.current = new SnakeEngine(canvas);

    // Start animation loop
    const animate = () => {
      if (engineRef.current) {
        engineRef.current.update();
        engineRef.current.draw();
      }
      animationRef.current = requestAnimationFrame(animate);
    };
    animate();

    // Start polling for current track
    const pollCurrentTrack = async () => {
      try {
        const track = await spotifyApi.getCurrentlyPlaying();
        if (track && track.item) {
          setCurrentTrack(track.item);
          setIsPlaying(track.is_playing);
          
          // Get audio features if track changed
          if (!audioFeatures || audioFeatures.id !== track.item.id) {
            const features = await spotifyApi.getAudioFeatures(track.item.id);
            setAudioFeatures(features);
            if (engineRef.current) {
              engineRef.current.updateAudioData(features);
            }
          }
        } else {
          setCurrentTrack(null);
          setIsPlaying(false);
        }
        setError(null);
      } catch (err) {
        console.error('Error polling track:', err);
        setError('Failed to get current track. Make sure Spotify is playing.');
      }
    };

    // Poll every 5 seconds
    const pollInterval = setInterval(pollCurrentTrack, 5000);
    pollCurrentTrack(); // Initial call

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      clearInterval(pollInterval);
    };
  }, [audioFeatures]);

  const handleLogout = () => {
    spotifyApi.logout();
    window.location.reload();
  };

  return (
    <div className="visualizer-container">
      <canvas ref={canvasRef} className="visualizer-canvas" />
      
      <div className="controls-overlay">
        <div className="track-info">
          {currentTrack ? (
            <div className="current-track">
              <div className="track-details">
                <h3>{currentTrack.name}</h3>
                <p>{currentTrack.artists.map(artist => artist.name).join(', ')}</p>
                <span className={`status ${isPlaying ? 'playing' : 'paused'}`}>
                  {isPlaying ? '▶️ Playing' : '⏸️ Paused'}
                </span>
              </div>
              {currentTrack.album.images[0] && (
                <img 
                  src={currentTrack.album.images[0].url} 
                  alt={currentTrack.album.name}
                  className="album-art"
                />
              )}
            </div>
          ) : (
            <div className="no-track">
              <p>🎵 Start playing music on Spotify to see the visualization!</p>
            </div>
          )}
        </div>

        {audioFeatures && (
          <div className="audio-features">
            <h4>Audio Analysis</h4>
            <div className="feature-bars">
              <div className="feature">
                <label>Energy</label>
                <div className="bar">
                  <div 
                    className="fill energy" 
                    style={{ width: `${audioFeatures.energy * 100}%` }}
                  ></div>
                </div>
                <span>{Math.round(audioFeatures.energy * 100)}%</span>
              </div>
              <div className="feature">
                <label>Valence</label>
                <div className="bar">
                  <div 
                    className="fill valence" 
                    style={{ width: `${audioFeatures.valence * 100}%` }}
                  ></div>
                </div>
                <span>{Math.round(audioFeatures.valence * 100)}%</span>
              </div>
              <div className="feature">
                <label>Tempo</label>
                <div className="bar">
                  <div 
                    className="fill tempo" 
                    style={{ width: `${Math.min(audioFeatures.tempo / 200, 1) * 100}%` }}
                  ></div>
                </div>
                <span>{Math.round(audioFeatures.tempo)} BPM</span>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="error-overlay">
            <p>{error}</p>
          </div>
        )}

        <div className="app-controls">
          <button onClick={handleLogout} className="logout-btn">
            Disconnect Spotify
          </button>
        </div>
      </div>
    </div>
  );
};

export default SnakeVisualizer;
