import { useState, useEffect } from 'react';
import spotifyApi from '../utils/spotifyApi';

const SpotifyAuth = ({ onAuthSuccess }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Check if we're returning from Spotify auth (implicit grant flow)
    if (window.location.hash.includes('access_token')) {
      handleAuthCallback();
    } else if (window.location.hash.includes('error')) {
      setError('Authentication failed. Please try again.');
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (spotifyApi.isAuthenticated()) {
      onAuthSuccess();
    }
  }, [onAuthSuccess]);

  const handleAuthCallback = () => {
    setIsLoading(true);
    setError(null);

    try {
      const success = spotifyApi.handleTokenFromUrl();
      if (success) {
        onAuthSuccess();
      } else {
        setError('Failed to authenticate with Spotify. Please try again.');
      }
    } catch (err) {
      setError('Authentication error. Please try again.');
      console.error('Auth error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = () => {
    setError(null);
    const authUrl = spotifyApi.getAuthUrl();
    window.location.href = authUrl;
  };

  const handleLogout = () => {
    spotifyApi.logout();
    window.location.reload();
  };

  if (spotifyApi.isAuthenticated()) {
    return (
      <div className="auth-container authenticated">
        <div className="auth-card">
          <h2>🎵 Connected to Spotify</h2>
          <p>Ready to visualize your music!</p>
          <button onClick={handleLogout} className="logout-btn">
            Disconnect
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-container">
      <div className="auth-card">
        <h1>🐍 Psychedelic Snake Visualizer</h1>
        <p>Connect your Spotify account to create trippy snake visualizations that react to your music!</p>
        
        {error && (
          <div className="error-message">
            {error}
          </div>
        )}
        
        <button 
          onClick={handleLogin} 
          disabled={isLoading}
          className="spotify-login-btn"
        >
          {isLoading ? 'Connecting...' : 'Connect with Spotify'}
        </button>
        
        <div className="features">
          <h3>Features:</h3>
          <ul>
            <li>🎨 Psychedelic snake animations</li>
            <li>🎵 Real-time music analysis</li>
            <li>🌈 Color-changing based on mood</li>
            <li>⚡ Movement synced to tempo</li>
            <li>✨ Trippy visual effects</li>
          </ul>
        </div>
        
        <div className="setup-note">
          <p><strong>Setup Required:</strong></p>
          <p>You'll need to create a Spotify app and add your Client ID to the .env file.</p>
          <p>Visit <a href="https://developer.spotify.com/dashboard" target="_blank" rel="noopener noreferrer">
            Spotify Developer Dashboard
          </a> to get started.</p>
        </div>
      </div>
    </div>
  );
};

export default SpotifyAuth;
