import { useState } from 'react';
import SpotifyAuth from './components/SpotifyAuth';
import SnakeVisualizer from './components/SnakeVisualizer';
import spotifyApi from './utils/spotifyApi';
import './App.css';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(spotifyApi.isAuthenticated());

  const handleAuthSuccess = () => {
    setIsAuthenticated(true);
  };

  return (
    <div className="App">
      {isAuthenticated ? (
        <SnakeVisualizer />
      ) : (
        <SpotifyAuth onAuthSuccess={handleAuthSuccess} />
      )}
    </div>
  );
}

export default App;
