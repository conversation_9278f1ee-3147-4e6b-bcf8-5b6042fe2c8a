import axios from 'axios';

const CLIENT_ID = import.meta.env.VITE_SPOTIFY_CLIENT_ID;
const REDIRECT_URI = import.meta.env.VITE_SPOTIFY_REDIRECT_URI;
const SCOPES = 'user-read-currently-playing user-read-playback-state';

class SpotifyAPI {
  constructor() {
    this.accessToken = localStorage.getItem('spotify_access_token');
    this.tokenExpiry = localStorage.getItem('spotify_token_expiry');
  }

  // Generate Spotify authorization URL using implicit grant flow
  getAuthUrl() {
    const params = new URLSearchParams({
      client_id: CLIENT_ID,
      response_type: 'token',
      redirect_uri: REDIRECT_URI,
      scope: SCOPES,
      show_dialog: 'true'
    });
    return `https://accounts.spotify.com/authorize?${params.toString()}`;
  }

  // Handle token from URL hash (implicit grant flow)
  handleTokenFromUrl() {
    const hash = window.location.hash.substring(1);
    const params = new URLSearchParams(hash);

    const access_token = params.get('access_token');
    const expires_in = params.get('expires_in');
    const error = params.get('error');

    if (error) {
      console.error('Spotify auth error:', error);
      return false;
    }

    if (access_token && expires_in) {
      const expiryTime = Date.now() + (parseInt(expires_in) * 1000);

      // Store tokens
      localStorage.setItem('spotify_access_token', access_token);
      localStorage.setItem('spotify_token_expiry', expiryTime.toString());

      this.accessToken = access_token;
      this.tokenExpiry = expiryTime.toString();

      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);

      return true;
    }

    return false;
  }

  // Check if user is authenticated
  isAuthenticated() {
    return this.accessToken && Date.now() < parseInt(this.tokenExpiry);
  }

  // Get currently playing track
  async getCurrentlyPlaying() {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated');
    }

    try {
      const response = await axios.get('https://api.spotify.com/v1/me/player/currently-playing', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error getting currently playing:', error);
      throw error;
    }
  }

  // Get audio features for a track
  async getAudioFeatures(trackId) {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated');
    }

    try {
      const response = await axios.get(`https://api.spotify.com/v1/audio-features/${trackId}`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error getting audio features:', error);
      throw error;
    }
  }

  // Get audio analysis for a track
  async getAudioAnalysis(trackId) {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated');
    }

    try {
      const response = await axios.get(`https://api.spotify.com/v1/audio-analysis/${trackId}`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error getting audio analysis:', error);
      throw error;
    }
  }

  // Logout
  logout() {
    localStorage.removeItem('spotify_access_token');
    localStorage.removeItem('spotify_token_expiry');
    this.accessToken = null;
    this.tokenExpiry = null;
  }
}

export default new SpotifyAPI();
