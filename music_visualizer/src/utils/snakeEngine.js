export class PsychedelicSnake {
  constructor(canvas, id = 0) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.id = id;
    
    // Snake properties
    this.segments = [];
    this.maxSegments = 50;
    this.segmentSize = 8;
    this.speed = 2;
    this.direction = Math.random() * Math.PI * 2;
    this.turnSpeed = 0.05;
    
    // Visual properties
    this.hue = Math.random() * 360;
    this.saturation = 80;
    this.lightness = 50;
    this.thickness = 5;
    this.trail = [];
    this.maxTrail = 100;
    
    // Audio reactive properties
    this.energy = 0;
    this.tempo = 120;
    this.valence = 0.5;
    this.loudness = 0;
    
    // Initialize position
    this.x = Math.random() * canvas.width;
    this.y = Math.random() * canvas.height;
    
    // Initialize segments
    for (let i = 0; i < this.maxSegments; i++) {
      this.segments.push({
        x: this.x - i * this.segmentSize,
        y: this.y,
        size: this.segmentSize * (1 - i / this.maxSegments),
        age: i
      });
    }
  }

  updateAudioData(audioFeatures) {
    if (audioFeatures) {
      this.energy = audioFeatures.energy || 0;
      this.tempo = audioFeatures.tempo || 120;
      this.valence = audioFeatures.valence || 0.5;
      this.loudness = Math.max(0, (audioFeatures.loudness + 60) / 60); // Normalize loudness
    }
  }

  update() {
    // Audio-reactive movement
    const energyMultiplier = 1 + this.energy * 2;
    const tempoMultiplier = this.tempo / 120;
    
    // Update direction based on audio
    this.direction += (Math.random() - 0.5) * this.turnSpeed * energyMultiplier;
    
    // Update speed based on tempo and energy
    const currentSpeed = this.speed * energyMultiplier * tempoMultiplier;
    
    // Move head
    this.x += Math.cos(this.direction) * currentSpeed;
    this.y += Math.sin(this.direction) * currentSpeed;
    
    // Wrap around screen
    if (this.x < 0) this.x = this.canvas.width;
    if (this.x > this.canvas.width) this.x = 0;
    if (this.y < 0) this.y = this.canvas.height;
    if (this.y > this.canvas.height) this.y = 0;
    
    // Add new head segment
    this.segments.unshift({
      x: this.x,
      y: this.y,
      size: this.segmentSize * (1 + this.energy),
      age: 0
    });
    
    // Remove tail if too long
    if (this.segments.length > this.maxSegments) {
      this.segments.pop();
    }
    
    // Update segment ages
    this.segments.forEach((segment, index) => {
      segment.age = index;
    });
    
    // Add to trail
    this.trail.unshift({ x: this.x, y: this.y, time: Date.now() });
    if (this.trail.length > this.maxTrail) {
      this.trail.pop();
    }
    
    // Update visual properties based on audio
    this.hue = (this.hue + this.valence * 5) % 360;
    this.thickness = 3 + this.loudness * 10;
  }

  draw() {
    const time = Date.now() * 0.001;
    
    // Draw trail with fading effect
    this.ctx.globalCompositeOperation = 'screen';
    for (let i = 0; i < this.trail.length - 1; i++) {
      const alpha = (1 - i / this.trail.length) * 0.3;
      const trailHue = (this.hue + i * 2) % 360;
      
      this.ctx.strokeStyle = `hsla(${trailHue}, ${this.saturation}%, ${this.lightness}%, ${alpha})`;
      this.ctx.lineWidth = this.thickness * alpha;
      this.ctx.lineCap = 'round';
      
      this.ctx.beginPath();
      this.ctx.moveTo(this.trail[i].x, this.trail[i].y);
      this.ctx.lineTo(this.trail[i + 1].x, this.trail[i + 1].y);
      this.ctx.stroke();
    }
    
    // Draw snake segments
    this.ctx.globalCompositeOperation = 'lighter';
    for (let i = this.segments.length - 1; i >= 0; i--) {
      const segment = this.segments[i];
      const alpha = 1 - (segment.age / this.maxSegments);
      const segmentHue = (this.hue + segment.age * 3 + time * 50) % 360;
      const pulsation = 1 + Math.sin(time * 5 + segment.age * 0.5) * 0.3 * this.energy;
      
      // Outer glow
      this.ctx.shadowColor = `hsl(${segmentHue}, ${this.saturation}%, ${this.lightness}%)`;
      this.ctx.shadowBlur = 20 * this.energy;
      
      // Main segment
      this.ctx.fillStyle = `hsla(${segmentHue}, ${this.saturation}%, ${this.lightness}%, ${alpha})`;
      this.ctx.beginPath();
      this.ctx.arc(
        segment.x, 
        segment.y, 
        segment.size * pulsation, 
        0, 
        Math.PI * 2
      );
      this.ctx.fill();
      
      // Inner bright core
      this.ctx.fillStyle = `hsla(${segmentHue}, ${this.saturation}%, ${Math.min(90, this.lightness + 30)}%, ${alpha * 0.8})`;
      this.ctx.beginPath();
      this.ctx.arc(
        segment.x, 
        segment.y, 
        segment.size * pulsation * 0.5, 
        0, 
        Math.PI * 2
      );
      this.ctx.fill();
    }
    
    // Reset shadow
    this.ctx.shadowBlur = 0;
    this.ctx.globalCompositeOperation = 'source-over';
  }
}

export class SnakeEngine {
  constructor(canvas) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.snakes = [];
    this.numSnakes = 3;
    this.audioFeatures = null;
    
    // Background effect properties
    this.backgroundHue = 0;
    this.particles = [];
    
    this.initSnakes();
    this.initParticles();
  }

  initSnakes() {
    for (let i = 0; i < this.numSnakes; i++) {
      this.snakes.push(new PsychedelicSnake(this.canvas, i));
    }
  }

  initParticles() {
    for (let i = 0; i < 50; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        size: Math.random() * 3 + 1,
        hue: Math.random() * 360,
        life: Math.random()
      });
    }
  }

  updateAudioData(audioFeatures) {
    this.audioFeatures = audioFeatures;
    this.snakes.forEach(snake => snake.updateAudioData(audioFeatures));
    
    if (audioFeatures) {
      this.backgroundHue = (this.backgroundHue + audioFeatures.valence * 2) % 360;
    }
  }

  update() {
    // Update snakes
    this.snakes.forEach(snake => snake.update());
    
    // Update particles
    this.particles.forEach(particle => {
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.life -= 0.01;
      
      if (particle.life <= 0 || particle.x < 0 || particle.x > this.canvas.width || 
          particle.y < 0 || particle.y > this.canvas.height) {
        particle.x = Math.random() * this.canvas.width;
        particle.y = Math.random() * this.canvas.height;
        particle.life = 1;
        particle.hue = Math.random() * 360;
      }
    });
  }

  draw() {
    // Create trippy background effect
    const gradient = this.ctx.createRadialGradient(
      this.canvas.width / 2, this.canvas.height / 2, 0,
      this.canvas.width / 2, this.canvas.height / 2, Math.max(this.canvas.width, this.canvas.height)
    );
    
    const energy = this.audioFeatures?.energy || 0;
    gradient.addColorStop(0, `hsla(${this.backgroundHue}, 30%, 5%, 0.1)`);
    gradient.addColorStop(1, `hsla(${(this.backgroundHue + 180) % 360}, 40%, 10%, 0.05)`);
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Draw particles
    this.ctx.globalCompositeOperation = 'screen';
    this.particles.forEach(particle => {
      const alpha = particle.life * 0.5;
      this.ctx.fillStyle = `hsla(${particle.hue}, 70%, 60%, ${alpha})`;
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      this.ctx.fill();
    });
    
    // Draw snakes
    this.snakes.forEach(snake => snake.draw());
  }

  resize(width, height) {
    this.canvas.width = width;
    this.canvas.height = height;
  }
}
