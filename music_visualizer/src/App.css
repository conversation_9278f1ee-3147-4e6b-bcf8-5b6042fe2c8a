/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #000;
  color: #fff;
  overflow: hidden;
}

.App {
  width: 100vw;
  height: 100vh;
}

/* Authentication Styles */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
  padding: 20px;
}

.auth-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  max-width: 500px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.auth-card h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.spotify-login-btn {
  background: #1db954;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 20px 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.spotify-login-btn:hover {
  background: #1ed760;
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(29, 185, 84, 0.3);
}

.spotify-login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.features {
  margin: 30px 0;
  text-align: left;
}

.features h3 {
  color: #4ecdc4;
  margin-bottom: 15px;
}

.features ul {
  list-style: none;
}

.features li {
  padding: 8px 0;
  border-left: 3px solid #ff6b6b;
  padding-left: 15px;
  margin: 10px 0;
}

.setup-note {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 10px;
  padding: 20px;
  margin-top: 20px;
}

.setup-note a {
  color: #4ecdc4;
  text-decoration: none;
}

.setup-note a:hover {
  text-decoration: underline;
}

.error-message {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid #ff6b6b;
  border-radius: 10px;
  padding: 15px;
  margin: 15px 0;
  color: #ff6b6b;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Visualizer Styles */
.visualizer-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.visualizer-canvas {
  display: block;
  background: #000;
}

.controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.controls-overlay > * {
  pointer-events: auto;
}

.track-info {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  max-width: 400px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.current-track {
  display: flex;
  align-items: center;
  gap: 15px;
}

.track-details h3 {
  font-size: 1.2rem;
  margin-bottom: 5px;
  color: #fff;
}

.track-details p {
  color: #ccc;
  margin-bottom: 10px;
}

.status {
  font-size: 0.9rem;
  padding: 5px 10px;
  border-radius: 15px;
  font-weight: 600;
}

.status.playing {
  background: rgba(29, 185, 84, 0.2);
  color: #1db954;
  border: 1px solid #1db954;
}

.status.paused {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid #ffc107;
}

.album-art {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  object-fit: cover;
}

.no-track {
  text-align: center;
  color: #ccc;
  font-style: italic;
}

.audio-features {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  min-width: 250px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.audio-features h4 {
  margin-bottom: 15px;
  color: #4ecdc4;
}

.feature-bars {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature {
  display: flex;
  align-items: center;
  gap: 10px;
}

.feature label {
  min-width: 60px;
  font-size: 0.9rem;
  color: #ccc;
}

.bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.fill.energy {
  background: linear-gradient(90deg, #ff6b6b, #ff8e53);
}

.fill.valence {
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

.fill.tempo {
  background: linear-gradient(90deg, #45b7d1, #96c93d);
}

.feature span {
  min-width: 50px;
  font-size: 0.8rem;
  color: #fff;
  text-align: right;
}

.app-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.error-overlay {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(255, 107, 107, 0.9);
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  border: 1px solid #ff6b6b;
}

/* Responsive Design */
@media (max-width: 768px) {
  .track-info {
    top: 10px;
    left: 10px;
    right: 10px;
    max-width: none;
  }

  .audio-features {
    top: auto;
    bottom: 80px;
    right: 10px;
    left: 10px;
  }

  .current-track {
    flex-direction: column;
    text-align: center;
  }

  .album-art {
    width: 60px;
    height: 60px;
  }
}
